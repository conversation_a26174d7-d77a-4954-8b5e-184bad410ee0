import fitz  # PyMuPDF
import os
import json
import pandas as pd
from groq import Groq  # ✅ correct import

# Step 1: Init client
client = Groq(api_key="********************************************************")

# Step 2: Extract text
pdf_path = "report1.pdf"  # your PDF path
doc = fitz.open(pdf_path)
raw_text = "\n".join([page.get_text() for page in doc])

# Step 3: Prompt
prompt = f"""
You are a medical lab report interpreter.

Extract ALL medically important parameters from the following lab report. 
Return the results as a JSON list of objects with the following fields:
- Parameter
- Value
- Unit
- Reference Range (if available)

Report content:
\"\"\"
{raw_text}
\"\"\"

Only return the JSON list. Do not include explanations or extra text.
"""

# Step 4: Call Groq API
response = client.chat.completions.create(
    model="llama3-70b-8192",
    messages=[{"role": "user", "content": prompt}],
    temperature=0
)

# Step 5: Parse result
text_response = response.choices[0].message.content.strip()
try:
    data = json.loads(text_response)
except json.JSONDecodeError:
    print("[!] Failed to parse JSON. Raw output:")
    print(text_response[:1000])
    data = []

# Step 6: Save result
df = pd.DataFrame(data)
df.to_csv("result_groq.csv", index=False)
print("[✓] Saved output to result_groq.csv")
