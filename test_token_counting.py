#!/usr/bin/env python3
"""
Test script for token counting and cost estimation
Run this to see how many tokens your GROQ API calls are using
"""

import os
import sys

# Add the app directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
app_dir = os.path.join(current_dir, 'app')
sys.path.insert(0, app_dir)

from services.token_counter import TokenCounter, track_api_call, api_logger, get_cost_projections

def test_token_estimation():
    """Test token estimation with sample medical report text"""
    
    print("🧪 Testing Token Estimation for Medical Report Processing")
    print("=" * 70)
    
    # Sample medical report text (typical size)
    sample_report = """
    LABORATORY REPORT
    
    Patient: John <PERSON>e
    Date: 2024-01-15
    
    COMPLETE BLOOD COUNT (CBC)
    Hemoglobin: 14.2 g/dL (Reference: 13.5-17.5)
    Hematocrit: 42.1% (Reference: 41-53)
    White Blood Cell Count: 7,200 /μL (Reference: 4,500-11,000)
    Red Blood Cell Count: 4.8 million/μL (Reference: 4.7-6.1)
    Platelet Count: 285,000 /μL (Reference: 150,000-450,000)
    
    BASIC METABOLIC PANEL
    Glucose: 95 mg/dL (Reference: 70-100)
    Sodium: 140 mEq/L (Reference: 136-145)
    Potassium: 4.2 mEq/L (Reference: 3.5-5.0)
    Chloride: 102 mEq/L (Reference: 98-107)
    CO2: 24 mEq/L (Reference: 22-28)
    BUN: 18 mg/dL (Reference: 7-20)
    Creatinine: 1.0 mg/dL (Reference: 0.6-1.2)
    
    LIPID PANEL
    Total Cholesterol: 185 mg/dL (Reference: <200)
    HDL Cholesterol: 55 mg/dL (Reference: >40)
    LDL Cholesterol: 110 mg/dL (Reference: <100)
    Triglycerides: 100 mg/dL (Reference: <150)
    
    LIVER FUNCTION TESTS
    ALT: 25 U/L (Reference: 7-56)
    AST: 22 U/L (Reference: 10-40)
    Alkaline Phosphatase: 75 U/L (Reference: 44-147)
    Total Bilirubin: 0.8 mg/dL (Reference: 0.3-1.2)
    """
    
    # Sample prompt (what we send to GROQ)
    prompt = f"""
You are a medical lab report parser.

Your task is to extract all medically important test parameters from the lab report below.
Return a JSON list where each item has the following fields:

- "Parameter": descriptive test name (e.g., "Hemoglobin", "Total Cholesterol")
- "Value": only the result value (e.g., "13.2" or "Positive")
- "Unit": unit of measurement (e.g., "g/dL", "mg/dL", "%")

Important:
- DO NOT extract reference ranges - only parameter names, values, and units
- DO NOT combine value and unit into one field
- Normalize units like "mg/dl" to "mg/dL"
- Use standard parameter names (e.g., "Total Cholesterol" not "CHOL")
- If unit is missing, leave the field blank
- Include all tests found in the report

Return only valid JSON. No commentary.

Report:
\"\"\"
{sample_report}
\"\"\"
"""
    
    # Sample response (what GROQ typically returns)
    sample_response = """[
  {"Parameter": "Hemoglobin", "Value": "14.2", "Unit": "g/dL"},
  {"Parameter": "Hematocrit", "Value": "42.1", "Unit": "%"},
  {"Parameter": "White Blood Cell Count", "Value": "7200", "Unit": "/μL"},
  {"Parameter": "Red Blood Cell Count", "Value": "4.8", "Unit": "million/μL"},
  {"Parameter": "Platelet Count", "Value": "285000", "Unit": "/μL"},
  {"Parameter": "Glucose", "Value": "95", "Unit": "mg/dL"},
  {"Parameter": "Sodium", "Value": "140", "Unit": "mEq/L"},
  {"Parameter": "Potassium", "Value": "4.2", "Unit": "mEq/L"},
  {"Parameter": "Chloride", "Value": "102", "Unit": "mEq/L"},
  {"Parameter": "CO2", "Value": "24", "Unit": "mEq/L"},
  {"Parameter": "BUN", "Value": "18", "Unit": "mg/dL"},
  {"Parameter": "Creatinine", "Value": "1.0", "Unit": "mg/dL"},
  {"Parameter": "Total Cholesterol", "Value": "185", "Unit": "mg/dL"},
  {"Parameter": "HDL Cholesterol", "Value": "55", "Unit": "mg/dL"},
  {"Parameter": "LDL Cholesterol", "Value": "110", "Unit": "mg/dL"},
  {"Parameter": "Triglycerides", "Value": "100", "Unit": "mg/dL"},
  {"Parameter": "ALT", "Value": "25", "Unit": "U/L"},
  {"Parameter": "AST", "Value": "22", "Unit": "U/L"},
  {"Parameter": "Alkaline Phosphatase", "Value": "75", "Unit": "U/L"},
  {"Parameter": "Total Bilirubin", "Value": "0.8", "Unit": "mg/dL"}
]"""
    
    # Test with different models
    models = ['llama3-70b-8192', 'llama3-8b-8192', 'mixtral-8x7b-32768']
    
    for model in models:
        print(f"\n📊 Analysis for {model}:")
        print("-" * 50)
        
        usage_info = TokenCounter.get_usage_info(prompt, sample_response, model)
        
        print(f"Prompt tokens: {usage_info['prompt_tokens']:,}")
        print(f"Completion tokens: {usage_info['completion_tokens']:,}")
        print(f"Total tokens: {usage_info['total_tokens']:,}")
        print(f"Estimated cost: ${usage_info['estimated_cost']:.6f}")
        
        # Track this call
        track_api_call(prompt, sample_response, model, 'extract_parameters')
    
    print("\n" + "=" * 70)
    
    # Show cost projections
    print("\n💰 Cost Projections:")
    print("=" * 70)
    
    # Assume we process 10 reports per day with similar token usage
    avg_tokens = TokenCounter.estimate_tokens(prompt) + TokenCounter.estimate_tokens(sample_response)
    
    for model in models:
        print(f"\n{model}:")
        projections = get_cost_projections(daily_calls=10, tokens_per_call=avg_tokens, model=model)
        
        print(f"  Cost per call: ${projections['cost_per_call']:.6f}")
        print(f"  Daily (10 calls): ${projections['daily_cost']:.4f}")
        print(f"  Weekly: ${projections['weekly_cost']:.2f}")
        print(f"  Monthly: ${projections['monthly_cost']:.2f}")
        print(f"  Yearly: ${projections['yearly_cost']:.2f}")
    
    # Show overall summary
    print("\n📈 Session Summary:")
    api_logger.print_summary()
    
    print("\n💡 Tips for Cost Optimization:")
    print("- Use llama3-8b-8192 for simpler extraction tasks (much cheaper)")
    print("- Keep prompts concise but clear")
    print("- Consider batch processing multiple reports in one call")
    print("- Monitor actual vs estimated token usage")
    print("- Set up alerts for daily/monthly cost thresholds")


if __name__ == "__main__":
    test_token_estimation()
