{% extends "base.html" %}

{% block title %}Report Details - Medical Report Tracker{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-medical"></i> Report Details
            </h1>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Report Information
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>File Name:</strong></td>
                        <td>{{ report.original_filename }}</td>
                    </tr>
                    <tr>
                        <td><strong>Upload Date:</strong></td>
                        <td>{{ report.upload_date.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            {% if report.status == 'processed' %}
                                <span class="badge bg-success">Processed</span>
                            {% elif report.status == 'processing' %}
                                <span class="badge bg-warning">Processing</span>
                            {% elif report.status == 'error' %}
                                <span class="badge bg-danger">Error</span>
                            {% else %}
                                <span class="badge bg-secondary">Pending</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Parameters Found:</strong></td>
                        <td>{{ parameters|length }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools"></i> Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('compare_reports') }}" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> Compare with Other Reports
                    </a>
                    <button class="btn btn-outline-success" onclick="exportToCSV()">
                        <i class="fas fa-download"></i> Export to CSV
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> Extracted Parameters
                </h6>
            </div>
            <div class="card-body">
                {% if parameters %}


                    <div class="table-responsive">
                        <table class="table table-hover" id="parametersTable">
                            <thead>
                                <tr>
                                    <th>Parameter</th>
                                    <th>Value</th>
                                    <th>Unit</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in analyzed_parameters %}
                                {% set param = item.parameter %}
                                {% set analysis = item.analysis %}
                                <tr class="parameter-row">
                                    <td><strong>{{ param.name }}</strong></td>
                                    <td>
                                        <span class="badge" style="background-color: {{ analysis.color }}; color: white;">
                                            {% if analysis.flag %}
                                                {{ analysis.flag }} {{ param.value or 'N/A' }}
                                            {% else %}
                                                {{ param.value or 'N/A' }}
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>{{ param.unit or '-' }}</td>
                                    <td class="status-cell">
                                        <span class="badge" style="background-color: {{ analysis.color }}; color: white;"
                                              title="{{ analysis.message }}">
                                            {{ analysis.message }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5 class="text-muted">No parameters extracted</h5>
                        <p class="text-muted">The report may need reprocessing or manual review.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
// Analysis is now done server-side, just initialize tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Analysis is now done server-side, this function is no longer needed

function showFlaggedAlert(count) {
    const alertHtml = `
        <div class="alert alert-warning alert-dismissible fade show mt-3" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Attention:</strong> ${count} parameter(s) are outside normal ranges.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const reportCard = document.querySelector('.card-body');
    reportCard.insertAdjacentHTML('afterbegin', alertHtml);
}

function exportToCSV() {
    const table = document.getElementById('parametersTable');
    const rows = table.querySelectorAll('tr');
    let csv = [];

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td, th');
        let csvRow = [];

        for (let j = 0; j < cols.length; j++) {
            csvRow.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
        }

        csv.push(csvRow.join(','));
    }

    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ report.original_filename.replace(".pdf", "_parameters.csv") }}';
    a.click();
    window.URL.revokeObjectURL(url);
}

// Initialize tooltips when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeTooltips();
});
</script>
{% endblock %}
