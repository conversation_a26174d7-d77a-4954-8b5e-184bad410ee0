{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["file_path = \"***********.pdf\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hello world\n"]}], "source": ["print(\"hello world\")\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Abnor<PERSON> Result(s) Summary\n", "Test Name\n", "Result Value\n", "Unit\n", "Reference Range\n", "Glyco Hemoglobin (HbA1c)\n", "HbA1C\n", "6.10\n", "% of total \n", "Hb\n", "<5.7: Normal\n", "5.7-6.4: <PERSON><PERSON><PERSON><PERSON>\n", ">=6.5: Diabetes\n", "Lipid Profile\n", "HDL Cholesterol\n", "36.02\n", "mg/dL\n", "40 - 60\n", "Liver Function Test\n", "S.G.P.T.\n", "64.58\n", "U/L\n", "< 45\n", "S.G.O.T.\n", "36.49\n", "U/L\n", "5.0 - 34.0\n", "Bilirubin Total\n", "1.23\n", "mg/dL\n", "0.3 - 1.2\n", "ESR\n", "2\n", "mm after \n", "1hr\n", "3 - 20\n", "Abnormal Result(s) Summary End\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ":\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ":\n", "Acc. Remarks\n", ":\n", "Ref Id2\n", ":\n", "Pt. Loc\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", ":\n", "Case ID\n", "***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", ":\n", "Page 1 of 10\n", "HB AND INDICES\n", "Haemoglobin\n", "G%\n", "13.00 - 17.00\n", "15.4\n", "RBC (Electrical Impedance)\n", "millions/cumm 4.50 - 5.50\n", "5.17\n", "PCV(Calc)\n", "%\n", "40.00 - 50.00\n", "46.89\n", "MCV (RBC histogram)\n", "fL\n", "83.00 - 101.00\n", "90.7\n", "MCH (Calc)\n", "pg\n", "27.00 - 32.00\n", "29.8\n", "MCHC (Calc)\n", "gm/dL\n", "31.50 - 34.50\n", "32.8\n", "RDW (RBC histogram)\n", "%\n", "11.00 - 16.00\n", "12.70\n", "TOTAL AND DIFFERENTIAL WBC COUNT (Flowcytometry)\n", "Total WBC Count\n", "/µL\n", "4000.00 - 10000.00\n", "7540\n", "Neutrophil\n", "%\n", "40.00 - 70.00\n", "63.6\n", "Lymphocyte\n", "%\n", "20.00 - 40.00\n", "26.3\n", "Eosinophil\n", "%\n", "1.00 - 6.00\n", "2.2\n", "Monocytes\n", "%\n", "2.00 - 10.00\n", "7.6\n", "Basophil\n", "%\n", "0.00 - 2.00\n", "0.3\n", "/µL 2000.00 - 7000.00\n", "4795\n", "/µL 1000.00 - 3000.00\n", "1983\n", "/µL 20.00 - 500.00\n", "166\n", "/µL 200.00 - 1000.00\n", "573\n", "/µL 0.00 - 100.00\n", "23\n", "Neut/Lympho Ratio (NLR)\n", "0.78 - 3.53\n", "2.42\n", "PLATELET COUNT (Optical)\n", "Platelet Count\n", "/µL\n", "150000.00 - 410000.00\n", "314000\n", "MPV\n", "fL\n", "6.5 - 12\n", "8.70\n", "PDW \n", "fL\n", "15 - 17\n", "15.9\n", "SMEAR STUDY\n", "RBC Morphology\n", "Normocytic Normochromic RBCs.\n", "WBC Morphology\n", "Total WBC count within normal limits.\n", "TEST\n", "RESULTS\n", "UNIT\n", "BIOLOGICAL REF. INTERVAL\n", "REMARKS\n", "HAEMOGRAM REPORT\n", "[ % ]\n", "EXPECTED VALUES\n", "[ Abs ]\n", "EXPECTED VALUES\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "<PERSON> <PERSON><PERSON>\n", "MD Pathology (G-62229)\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": Whole Blood EDTA\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 11:52\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 2 of 10\n", "Platelet\n", "Platelets are adequate in number.\n", "Parasite\n", "Malarial Parasite not seen on smear.\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "<PERSON> <PERSON><PERSON>\n", "MD Pathology (G-62229)\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": Whole Blood EDTA\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 11:52\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 3 of 10\n", "ESR\n", "L\n", "mm after 1hr\n", "3 - 20\n", "Photometrical capillary stopped flow kinetic \n", "analysis\n", "2\n", "BIOCHEMICAL INVESTIGATIONS\n", "Plasma Glucose - F\n", "mg/dL\n", "70.0 - 100\n", "Photometric,Hexokinase\n", "86.89\n", "Glycated Haemoglobin Estimation\n", "HbA1C\n", "H\n", "% of total Hb\n", "<5.7: Normal\n", "5.7-6.4: <PERSON><PERSON><PERSON><PERSON>\n", ">=6.5: Diabetes\n", "HPLC\n", "6.10\n", "Estimated Avg Glucose (3 Mths)\n", "mg/dL\n", "Not available\n", "Calculated\n", "128.37\n", " \n", "Interpretation :\n", "HbA1C level reflects the mean glucose concentration over previous 8-12 weeks and provides better indication of long term glycemic control.\n", " Levels of HbA1C may be low as result of shortened RBC life span in case of hemolytic anemia.\n", "Increased HbA1C values may be found in patients with polycythemia or post splenectomy patients.\n", "Patients with Homozygous forms of rare variant Hb(CC,SS,EE,SC) HbA1c can not be quantitated as there is no HbA.\n", "In such circumstances glycemic control can be monitored using plasma glucose levels or serum Fructosamine.\n", "The A1c target should be individualized based on numerous factors, such as age, life expectancy,comorbid conditions, duration of diabetes,\n", "risk of hypoglycemia or adverse consequences from hypoglycemia, patient motivation and adherence.\n", "Skeletal Profile\n", "Calcium\n", "mg/dL\n", "8.4 - 10.2\n", "OCPC\n", "8.70\n", "Phosphorus Inorganic\n", "mg/dL\n", "2.3 - 4.7\n", "Phosphomolybdate\n", "3.40\n", "RESULTS\n", "TEST\n", "UNIT\n", "BIOLOGICAL REF RANGE REMARKS\n", "HAEMATOLOGY INVESTIGATIONS\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "<PERSON> <PERSON><PERSON>\n", "Dr. <PERSON><PERSON>\n", "MD Pathology (G-62229)\n", "DCP, DNB (PATH) G-22473\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": Whole Blood EDTA,Plasma \n", "Fluoride F,Serum\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 12:28\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 4 of 10\n", "S.G.P.T.\n", "H\n", "U/L\n", "< 45\n", "NADH (Without P-5-P)\n", "64.58\n", "S.G.O.T.\n", "H\n", "U/L\n", "5.0 - 34.0\n", "NADH (Without P-5-P)\n", "36.49\n", "Alkaline Phosphatase\n", "U/L\n", "50.0 - 116.0\n", "Enzymatic, PNPP-AMP\n", "80.64\n", "Gamma  Glutamyl Transferase\n", "U/L\n", "0 - 55\n", "Multipoint Rate-L-y-Glytamyl-p-nitroanilide\n", "27.00\n", "Proteins (Total)\n", "gm/dL\n", "6.40 - 8.30\n", "B<PERSON>ret\n", "6.81\n", "Albumin\n", "gm/dL\n", "3.5 - 5.2\n", "(BCG)\n", "4.45\n", "Globulin\n", "gm/dL\n", "2 - 4.1\n", "2.36\n", "A/G Ratio\n", "1.0 - 2.1\n", "Calculated\n", "1.89\n", "Bilirubin Total\n", "H\n", "mg/dL\n", "0.3 - 1.2\n", "Colorimetric Diazo Method\n", "1.23\n", "Bilirubin Conjugated\n", "mg/dL\n", "0 - 0.50\n", "Colorimetric Diazo Method\n", "0.44\n", "Bilirubin Unconjugated\n", "mg/dL\n", "0 - 0.8\n", "0.79\n", "RESULTS\n", "TEST\n", "UNIT\n", "BIOLOGICAL REF RANGE REMARKS\n", "BIOCHEMICAL INVESTIGATIONS\n", "Liver Function Test\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "Dr. <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON> (Pathologist) G-34307\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": Serum\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 13:35\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 5 of 10\n", "Urea\n", "mg/dL\n", "17.97 - 54.99\n", "Urease\n", "20.79\n", "Creatinine\n", "mg/dL\n", "0.6 - 1.3\n", "Jaffe - Kinetic\n", "0.75\n", "Uric Acid\n", "mg/dL\n", "3.5 - 7.2\n", "Uricase\n", "5.21\n", "Sodium\n", "mmol/L\n", "136 - 145\n", "ISE\n", "137.00\n", "Potassium\n", "mmol/L\n", "3.5 - 5.1\n", "ISE\n", "4.10\n", "Chloride\n", "mmol/L\n", "98 - 107\n", "ISE\n", "100.00\n", "RESULTS\n", "TEST\n", "UNIT\n", "BIOLOGICAL REF RANGE REMARKS\n", "BIOCHEMICAL INVESTIGATIONS\n", "Renal Function Test\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "Dr. <PERSON><PERSON>\n", "DCP, DNB (PATH) G-22473\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": Serum\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 12:28\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 6 of 10\n", "Cholesterol\n", "mg/dL\n", "110 - 200\n", "Enzymatic\n", "117.85\n", "HDL Cholesterol\n", "L\n", "mg/dL\n", "40 - 60\n", "Accelerator Selective Detergent\n", "36.02\n", "Triglyceride\n", "mg/dL\n", "<150\n", "Arsenazo - Colorimetric\n", "58.80\n", "VLDL\n", "mg/dL\n", "10 - 40\n", "11.76\n", "Chol/HDL\n", "0 - 4.1\n", "Calculated\n", "3.27\n", "LDL Cholesterol\n", "mg/dL\n", "0.00 - 100.00\n", "Calculated\n", "70.07\n", "RESULTS\n", "TEST\n", "UNIT\n", "BIOLOGICAL REF RANGE REMARKS\n", "BIOCHEMICAL INVESTIGATIONS\n", "Lipid Profile\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "Dr. <PERSON><PERSON>\n", "DCP, DNB (PATH) G-22473\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": Serum\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 12:28\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 7 of 10\n", "Prostate Specific Antigen\n", "ng/mL\n", "0.00 - 4.00\n", "CMIA\n", "0.693\n", "TEST\n", "RESULTS\n", "UNIT\n", "BIOLOGICAL REF RANGE\n", "REMARKS\n", "Prostate Specific Antigen (PSA)\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "Dr. <PERSON><PERSON>\n", "DCP, DNB (PATH) G-22473\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": Serum\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 12:29\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 8 of 10\n", "Granular Cast\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "RBC cast\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "WBC Cast\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "<PERSON><PERSON>\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Mixed Cast\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Crystals\n", "Crystals\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Calcium oxalate \n", "Monohydrate\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Calcium oxalate Dihydrate\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Triple phosphate\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Uric acid\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "<PERSON><PERSON><PERSON> \n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Amorphous \n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Trichomonas\n", "/µL\n", "<PERSON>l\n", "<PERSON>l\n", "Glucose\n", "Negative\n", "Negative\n", "Ketone Bodies Urine\n", "Negative\n", "Negative\n", "Urobilinogen\n", "Negative\n", "Negative\n", "Bilirubin\n", "Negative\n", "Negative\n", "Nitrite\n", "Negative\n", "Negative\n", "Blood\n", "Negative\n", "Negative\n", "Colour\n", "Yellow\n", "Appearance\n", "Clear\n", "Sp.Gravity\n", "1.003 - \n", "1.035\n", "1.019\n", "pH\n", "4.6 - 8\n", "6.00\n", "Leucocytes (ESTERASE)\n", "Negative\n", "Negative\n", "<PERSON><PERSON>\n", "Negative\n", "Negative\n", "Leucocyte\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Leucocyte Clumps\n", "/HPF\n", "<PERSON>l\n", "RBC (Isomorphic)\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "RBC (Dysmorphic)\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Epithelial cells\n", "Squamous EC\n", "/HPF\n", "Present(+)\n", "Present\n", "NonSquamous EC\n", "/HPF\n", "<PERSON>l\n", "Transitional EC\n", "/HPF\n", "<PERSON>l\n", "Renal Tubular EC\n", "/HPF\n", "<PERSON>l\n", "Bacteria\n", "Bacteria\n", "/µL\n", "<PERSON>l\n", "<PERSON>l\n", "Bacteria Rods\n", "/µL\n", "<PERSON>l\n", "<PERSON>l\n", "Bacteria Cocci\n", "/µL\n", "<PERSON>l\n", "<PERSON>l\n", "Yeast\n", "/µL\n", "<PERSON>l\n", "<PERSON>l\n", "Cast\n", "Hyaline Cast\n", "/HPF\n", "<PERSON>l\n", "<PERSON>l\n", "Pathological Cast\n", "/HPF\n", "NIL\n", "<PERSON>l\n", "Hyaline granular cast\n", "/HPF\n", "<PERSON>l\n", "Automated Microscopy\n", "Physical and Chemical examination\n", "URINE EXAMINATION (STRIP METHOD AND AUTOMATED IMAGE EVALUATION)\n", "UNIT\n", "RESULTS\n", "TEST\n", "BRI\n", "UNIT\n", "RESULTS\n", "BRI\n", "TEST\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "Dr. <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON> (Pathologist) G-34307\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": <PERSON>\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 12:12\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 9 of 10\n", "------------------ End Of Report ------------------\n", "# For test performed on specimens received or collected from non-NSRL locations, it is presumed that the specimen belongs to the patient named or identified as labeled \n", "on the container/test request and such verification has been carried out at the point generation of the said specimen by the sender. NSRL will be responsible Only for the \n", "analytical part of test carried out. All other responsibility will be of referring Laboratory.\n", "Note:(LL-<PERSON><PERSON><PERSON>,L-<PERSON>,H-High,HH-VeryHigh\n", ",A-Abnormal)\n", "Printed On :\n", "04-Jul-2025 13:58\n", "Dr. <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON> (Pathologist) G-34307\n", "Reg Date and Time\n", ": 04-Jul-2025 11:04\n", "Sample Type\n", ": <PERSON>\n", "Mobile No. : **********\n", "Sample Date and Time\n", ":\n", "Sample Coll. By\n", ": NSRL\n", "Ref Id1\n", ":\n", "Report Date and Time\n", ": 04-Jul-2025 12:12\n", "Acc. Remarks\n", ": -\n", "Ref Id2\n", ":\n", "04-Jul-2025 09:39\n", ":\n", "Pt. Loc\n", "MC-6136\n", "LABORATORY REPORT\n", "Name\n", ": PRERAK SHAH\n", "Sex/Age : Male\n", "/ 51 Years\n", "Case ID : ***********\n", "Ref. By\n", ": Self\n", "Dis. At\n", ":\n", "Pt. ID\n", ": 4038327\n", "Bill. Loc. :\n", "Page 10 of 10\n", "\n"]}], "source": ["import fitz  # PyMuPDF\n", "import requests\n", "\n", "# Load and extract text from PDF\n", "def extract_text_from_pdf(file_path_param):\n", "    doc = fitz.open(file_path_param)\n", "    text = \"\"\n", "    for page in doc:\n", "        text += page.get_text()\n", "    return text\n", "\n", "# Send to Ollama\n", "def query_ollama(prompt, model=\"llama3\"):\n", "    response = requests.post(\n", "        \"http://localhost:11434/api/generate\",\n", "        json={\"model\": model, \"prompt\": prompt, \"stream\": False}\n", "    )\n", "    return response.json()[\"response\"]\n", "\n", "\n", "# Extract text from PDF\n", "pdf_text = extract_text_from_pdf(file_path)\n", "\n", "print(pdf_text)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[+] Parsing section: GENERAL\n", "[+] Parsing section: Glyco Hemoglobin (Hba1C)\n", "[+] Parsing section: Lipid Profile\n", "[+] Parsing section: Liver Function Test\n", "[+] Parsing section: Esr\n", "[+] Parsing section: Total And Differential Wbc Count (Flowcytometry)\n", "[!] Could not parse JSON for section: Total And Differential Wbc Count (Flowcytometry)\n", "[+] Parsing section: Total Wbc Count\n", "[!] Could not parse JSON for section: Total Wbc Count\n", "[+] Parsing section: Platelet Count (Optical)\n", "[!] Could not parse JSON for section: Platelet Count (Optical)\n", "[+] Parsing section: Platelet Count\n", "[!] Could not parse JSON for section: Platelet Count\n", "[+] Parsing section: Smear Study\n", "[+] Parsing section: Wbc Morphology\n", "[+] Parsing section: Total Wbc Count Within Normal Limits.\n", "[+] Parsing section: Haemogram Report\n", "[+] Parsing section: Platelet\n", "[+] Parsing section: Platelets Are Adequate In Number.\n", "[!] Could not parse JSON for section: Platelets Are Adequate In Number.\n", "[+] Parsing section: Renal Function Test\n", "[+] Parsing section: Wbc Cast\n", "[!] Could not parse JSO<PERSON> for section: Wbc Cast\n", "[+] Parsing section: Urine Examination (Strip Method And Automated Image Evaluation)\n", "[!] Could not parse JSON for section: Urine Examination (Strip Method And Automated Image Evaluation)\n", "                                           Parameter  \\\n", "0                                          Test Name   \n", "1                         Abnormal Result(s) Summary   \n", "2                                       Result Value   \n", "3                                               Unit   \n", "4                                    Reference Range   \n", "5                                              HbA1C   \n", "6                    Prostate Specific Antigen (PSA)   \n", "7                                               Urea   \n", "8                                             Urease   \n", "9                                         Creatinine   \n", "10                                   Jaffe - Kinetic   \n", "11                                         Uric Acid   \n", "12                                           Uricase   \n", "13                                            Sodium   \n", "14                                               ISE   \n", "15                                         Potassium   \n", "16                                               ISE   \n", "17                                          Chloride   \n", "18                          Plasma Glucose (Fasting)   \n", "19                      Glycated Haemoglobin (HbA1C)   \n", "20                    Estimated Avg Glucose (3 Mths)   \n", "21                                           Calcium   \n", "22                              Phosphorus Inorganic   \n", "23                                    RBC Morphology   \n", "24                      WBC (White Blood Cell) Count   \n", "25                                   Neutrophils (%)   \n", "26                                   Lymphocytes (%)   \n", "27                                     Monocytes (%)   \n", "28                                   Eosinophils (%)   \n", "29                                     Basophils (%)   \n", "30                                   Total WBC Count   \n", "31                                        Percentage   \n", "32                                    Absolute Value   \n", "33                             Red Blood Cells (RBC)   \n", "34                           White Blood Cells (WBC)   \n", "35                                  Haemoglobin (Hb)   \n", "36                                 Haematocrit (Hct)   \n", "37                 Mean Corpuscular Hemoglobin (MCH)   \n", "38  Mean Corpuscular Hemoglobin Concentration (MCHC)   \n", "39                     Mean Corpuscular Volume (MCV)   \n", "40                 Red Cell Distribution Width (RDW)   \n", "41                                         Platelets   \n", "42                                    Platelet Count   \n", "43                                       Cholesterol   \n", "44                                   HDL Cholesterol   \n", "45                                      Triglyceride   \n", "46                                              VLDL   \n", "47                                          Chol/HDL   \n", "48                                   LDL Cholesterol   \n", "\n", "                                                Value  \\\n", "0   Abnormal Result(s) Summary not available for t...   \n", "1                                  Abnormal Result(s)   \n", "2              Value not available for this parameter   \n", "3                                                       \n", "4     Not applicable (as the test result is abnormal)   \n", "5                                                6.10   \n", "6                                               0.693   \n", "7                                               17.97   \n", "8                                               20.79   \n", "9                                                 0.6   \n", "10                                               0.75   \n", "11                                                3.5   \n", "12                                               5.21   \n", "13                                                136   \n", "14                                             137.00   \n", "15                                               4.10   \n", "16                                             100.00   \n", "17                                                 98   \n", "18                                              86.89   \n", "19                                               6.10   \n", "20                                             128.37   \n", "21                                                8.4   \n", "22                                                2.3   \n", "23                            Normocytic Normochromic   \n", "24                                     5.2 x 10^3/¼uL   \n", "25                                                75%   \n", "26                                                18%   \n", "27                                                 3%   \n", "28                                                 1%   \n", "29                                               0.5%   \n", "30                               Within Normal Limits   \n", "31                                                  %   \n", "32                                                      \n", "33                                                      \n", "34                                                      \n", "35                                                      \n", "36                                                      \n", "37                                                      \n", "38                                                      \n", "39                                                      \n", "40                                                      \n", "41                                                      \n", "42                                            200,000   \n", "43                                             117.85   \n", "44                                              36.02   \n", "45                                              58.80   \n", "46                                              11.76   \n", "47                                               3.27   \n", "48                                              70.07   \n", "\n", "                                                 Unit  \\\n", "0                                                       \n", "1                                                       \n", "2   Abnormal Result(s) Summary not available for t...   \n", "3   Abnormal Result(s) Summary not available for t...   \n", "4                                                       \n", "5                                                   %   \n", "6                                               ng/mL   \n", "7                                               mg/dL   \n", "8                                                       \n", "9                                               mg/dL   \n", "10                                                      \n", "11                                              mg/dL   \n", "12                                                      \n", "13                                             mmol/L   \n", "14                                             mmol/L   \n", "15                                             mmol/L   \n", "16                                             mmol/L   \n", "17                                             mmol/L   \n", "18                                              mg/dL   \n", "19                                      % of total Hb   \n", "20                                              mg/dL   \n", "21                                              mg/dL   \n", "22                                              mg/dL   \n", "23                                                      \n", "24                                       cells per μL   \n", "25                                                  %   \n", "26                                                  %   \n", "27                                                  %   \n", "28                                                  %   \n", "29                                                  %   \n", "30                                                      \n", "31                                                      \n", "32                                          Cells/mcL   \n", "33                                     10^6 cells/mcL   \n", "34                                     10^3 cells/mcL   \n", "35                                               g/dL   \n", "36                                                  %   \n", "37                                                 pg   \n", "38                                               g/dL   \n", "39                                                 fl   \n", "40                                                  %   \n", "41                                           10^3/mcL   \n", "42                                                 µL   \n", "43                                              mg/dL   \n", "44                                              mg/dL   \n", "45                                              mg/dL   \n", "46                                              mg/dL   \n", "47                                                      \n", "48                                              mg/dL   \n", "\n", "                                      Reference Range  \n", "0                                                      \n", "1                                                      \n", "2                                                      \n", "3                                                      \n", "4                                                      \n", "5   Normal: <5.7, Prediabetes: 5.7-6.4, Diabetes: ...  \n", "6                                         0.00 - 4.00  \n", "7                                       17.97 - 54.99  \n", "8                                                 NaN  \n", "9                                           0.6 - 1.3  \n", "10                                                NaN  \n", "11                                          3.5 - 7.2  \n", "12                                                NaN  \n", "13                                          136 - 145  \n", "14                                               None  \n", "15                                          3.5 - 5.1  \n", "16                                               None  \n", "17                                           98 - 107  \n", "18                                         70.0 - 100  \n", "19  <5.7: Normal, 5.7-6.4: Prediabetes, >=6.5: Dia...  \n", "20                                      Not available  \n", "21                                         8.4 - 10.2  \n", "22                                          2.3 - 4.7  \n", "23                                                     \n", "24                              (4.0-11.0) x 10^3/¼uL  \n", "25                                          (40-70) %  \n", "26                                          (20-45) %  \n", "27                                            (2-8) %  \n", "28                                            (0-6) %  \n", "29                                                <2%  \n", "30                                                     \n", "31                                                     \n", "32                   Not provided in the given report  \n", "33                   4.2-5.4 (Male), 3.8-5.0 (Female)  \n", "34                                           4.8-10.8  \n", "35               13.5-17.5 (Male), 12.0-15.5 (Female)  \n", "36               40.7-50.3 (Male), 36.1-44.3 (Female)  \n", "37                                              27-32  \n", "38                                          31.5-36.0  \n", "39                                              82-98  \n", "40                                          11.5-14.5  \n", "41                                            150-450  \n", "42                                  150,000 - 450,000  \n", "43                                          110 - 200  \n", "44                                            40 - 60  \n", "45                                               <150  \n", "46                                            10 - 40  \n", "47                                            0 - 4.1  \n", "48                                      0.00 - 100.00  \n", "[✓] Saved structured data to parsed_medical_report.csv\n"]}], "source": ["\n", "import fitz  # PyMuPDF\n", "import re\n", "import json\n", "import pandas as pd\n", "import ollama\n", "\n", "# 1. Extract raw text from PDF\n", "def extract_text_from_pdf(pdf_path):\n", "    doc = fitz.open(pdf_path)\n", "    return \"\\n\".join(page.get_text() for page in doc)\n", "\n", "# 2. Preprocess and split into logical chunks\n", "def split_into_sections(text):\n", "    headers = [\n", "        \"HAEMOGRAM\", \"LIVER FUNCTION TEST\", \"RENAL FUNCTION TEST\", \"LIPID PROFILE\",\n", "        \"URINE EXAMINATION\", \"GLYCO HEMOGLOBIN\", \"SMEAR STUDY\", \"PLATELET\", \"WBC\", \"ESR\"\n", "    ]\n", "    section_map = {}\n", "    current = \"GENERAL\"\n", "    for line in text.split('\\n'):\n", "        upper_line = line.strip().upper()\n", "        if any(header in upper_line for header in headers):\n", "            current = upper_line.title()\n", "            section_map[current] = []\n", "        else:\n", "            section_map.setdefault(current, []).append(line.strip())\n", "    return section_map\n", "\n", "# 3. Call Ollama LLM on each section\n", "def parse_section_with_llm(section_name, section_lines, model='mistral'):\n", "    prompt = f\"\"\"\n", "You are a medical lab report interpreter. Given a section of a lab report, extract ALL medically relevant parameters and return them as structured JSON with the following fields:\n", "\n", "[\n", "  {{\n", "    \"Parameter\": string,\n", "    \"Value\": string,\n", "    \"Unit\": string,\n", "    \"Reference Range\": string (if available)\n", "  }},\n", "  ...\n", "]\n", "\n", "Section: {section_name}\n", "Content:\n", "{'\\n'.join(section_lines)}\n", "\n", "If nothing useful is present, return an empty list.\n", "\"\"\"\n", "    response = ollama.chat(model=model, messages=[{\"role\": \"user\", \"content\": prompt}])\n", "    try:\n", "        return json.loads(response['message']['content'])\n", "    except json.JSONDecodeError:\n", "        print(f\"[!] Could not parse JSON for section: {section_name}\")\n", "        return []\n", "\n", "# 4. Full pipeline\n", "def parse_medical_report(pdf_path):\n", "    raw_text = extract_text_from_pdf(file_path)\n", "    sections = split_into_sections(raw_text)\n", "    all_data = []\n", "\n", "    for section, lines in sections.items():\n", "        print(f\"[+] Parsing section: {section}\")\n", "        records = parse_section_with_llm(section, lines)\n", "        all_data.extend(records)\n", "\n", "    df = pd.DataFrame(all_data)\n", "    df = df[['Parameter', 'Value', 'Unit', 'Reference Range']]  # Column order\n", "    return df\n", "\n", "# 5. Run as script\n", "if __name__ == \"__main__\":\n", "    import sys\n", "    if len(sys.argv) < 2:\n", "        print(\"Usage: python parse_medical_report.py report.pdf\")\n", "    else:\n", "        df = parse_medical_report(sys.argv[1])\n", "        print(df)\n", "        df.to_csv(\"parsed_medical_report.csv\", index=False)\n", "        print(\"[✓] Saved structured data to parsed_medical_report.csv\")\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bound method NDFrame.head of                                            Parameter  \\\n", "0                                          Test Name   \n", "1                         Abnormal Result(s) Summary   \n", "2                                       Result Value   \n", "3                                               Unit   \n", "4                                    Reference Range   \n", "5                                              HbA1C   \n", "6                    Prostate Specific Antigen (PSA)   \n", "7                                               Urea   \n", "8                                             Urease   \n", "9                                         Creatinine   \n", "10                                   Jaffe - Kinetic   \n", "11                                         Uric Acid   \n", "12                                           Uricase   \n", "13                                            Sodium   \n", "14                                               ISE   \n", "15                                         Potassium   \n", "16                                               ISE   \n", "17                                          Chloride   \n", "18                          Plasma Glucose (Fasting)   \n", "19                      Glycated Haemoglobin (HbA1C)   \n", "20                    Estimated Avg Glucose (3 Mths)   \n", "21                                           Calcium   \n", "22                              Phosphorus Inorganic   \n", "23                                    RBC Morphology   \n", "24                      WBC (White Blood Cell) Count   \n", "25                                   Neutrophils (%)   \n", "26                                   Lymphocytes (%)   \n", "27                                     Monocytes (%)   \n", "28                                   Eosinophils (%)   \n", "29                                     Basophils (%)   \n", "30                                   Total WBC Count   \n", "31                                        Percentage   \n", "32                                    Absolute Value   \n", "33                             Red Blood Cells (RBC)   \n", "34                           White Blood Cells (WBC)   \n", "35                                  Haemoglobin (Hb)   \n", "36                                 Haematocrit (Hct)   \n", "37                 Mean Corpuscular Hemoglobin (MCH)   \n", "38  Mean Corpuscular Hemoglobin Concentration (MCHC)   \n", "39                     Mean Corpuscular Volume (MCV)   \n", "40                 Red Cell Distribution Width (RDW)   \n", "41                                         Platelets   \n", "42                                    Platelet Count   \n", "43                                       Cholesterol   \n", "44                                   HDL Cholesterol   \n", "45                                      Triglyceride   \n", "46                                              VLDL   \n", "47                                          Chol/HDL   \n", "48                                   LDL Cholesterol   \n", "\n", "                                                Value  \\\n", "0   Abnormal Result(s) Summary not available for t...   \n", "1                                  Abnormal Result(s)   \n", "2              Value not available for this parameter   \n", "3                                                       \n", "4     Not applicable (as the test result is abnormal)   \n", "5                                                6.10   \n", "6                                               0.693   \n", "7                                               17.97   \n", "8                                               20.79   \n", "9                                                 0.6   \n", "10                                               0.75   \n", "11                                                3.5   \n", "12                                               5.21   \n", "13                                                136   \n", "14                                             137.00   \n", "15                                               4.10   \n", "16                                             100.00   \n", "17                                                 98   \n", "18                                              86.89   \n", "19                                               6.10   \n", "20                                             128.37   \n", "21                                                8.4   \n", "22                                                2.3   \n", "23                            Normocytic Normochromic   \n", "24                                     5.2 x 10^3/¼uL   \n", "25                                                75%   \n", "26                                                18%   \n", "27                                                 3%   \n", "28                                                 1%   \n", "29                                               0.5%   \n", "30                               Within Normal Limits   \n", "31                                                  %   \n", "32                                                      \n", "33                                                      \n", "34                                                      \n", "35                                                      \n", "36                                                      \n", "37                                                      \n", "38                                                      \n", "39                                                      \n", "40                                                      \n", "41                                                      \n", "42                                            200,000   \n", "43                                             117.85   \n", "44                                              36.02   \n", "45                                              58.80   \n", "46                                              11.76   \n", "47                                               3.27   \n", "48                                              70.07   \n", "\n", "                                                 Unit  \\\n", "0                                                       \n", "1                                                       \n", "2   Abnormal Result(s) Summary not available for t...   \n", "3   Abnormal Result(s) Summary not available for t...   \n", "4                                                       \n", "5                                                   %   \n", "6                                               ng/mL   \n", "7                                               mg/dL   \n", "8                                                       \n", "9                                               mg/dL   \n", "10                                                      \n", "11                                              mg/dL   \n", "12                                                      \n", "13                                             mmol/L   \n", "14                                             mmol/L   \n", "15                                             mmol/L   \n", "16                                             mmol/L   \n", "17                                             mmol/L   \n", "18                                              mg/dL   \n", "19                                      % of total Hb   \n", "20                                              mg/dL   \n", "21                                              mg/dL   \n", "22                                              mg/dL   \n", "23                                                      \n", "24                                       cells per μL   \n", "25                                                  %   \n", "26                                                  %   \n", "27                                                  %   \n", "28                                                  %   \n", "29                                                  %   \n", "30                                                      \n", "31                                                      \n", "32                                          Cells/mcL   \n", "33                                     10^6 cells/mcL   \n", "34                                     10^3 cells/mcL   \n", "35                                               g/dL   \n", "36                                                  %   \n", "37                                                 pg   \n", "38                                               g/dL   \n", "39                                                 fl   \n", "40                                                  %   \n", "41                                           10^3/mcL   \n", "42                                                 µL   \n", "43                                              mg/dL   \n", "44                                              mg/dL   \n", "45                                              mg/dL   \n", "46                                              mg/dL   \n", "47                                                      \n", "48                                              mg/dL   \n", "\n", "                                      Reference Range  \n", "0                                                      \n", "1                                                      \n", "2                                                      \n", "3                                                      \n", "4                                                      \n", "5   Normal: <5.7, Prediabetes: 5.7-6.4, Diabetes: ...  \n", "6                                         0.00 - 4.00  \n", "7                                       17.97 - 54.99  \n", "8                                                 NaN  \n", "9                                           0.6 - 1.3  \n", "10                                                NaN  \n", "11                                          3.5 - 7.2  \n", "12                                                NaN  \n", "13                                          136 - 145  \n", "14                                               None  \n", "15                                          3.5 - 5.1  \n", "16                                               None  \n", "17                                           98 - 107  \n", "18                                         70.0 - 100  \n", "19  <5.7: Normal, 5.7-6.4: Prediabetes, >=6.5: Dia...  \n", "20                                      Not available  \n", "21                                         8.4 - 10.2  \n", "22                                          2.3 - 4.7  \n", "23                                                     \n", "24                              (4.0-11.0) x 10^3/¼uL  \n", "25                                          (40-70) %  \n", "26                                          (20-45) %  \n", "27                                            (2-8) %  \n", "28                                            (0-6) %  \n", "29                                                <2%  \n", "30                                                     \n", "31                                                     \n", "32                   Not provided in the given report  \n", "33                   4.2-5.4 (Male), 3.8-5.0 (Female)  \n", "34                                           4.8-10.8  \n", "35               13.5-17.5 (Male), 12.0-15.5 (Female)  \n", "36               40.7-50.3 (Male), 36.1-44.3 (Female)  \n", "37                                              27-32  \n", "38                                          31.5-36.0  \n", "39                                              82-98  \n", "40                                          11.5-14.5  \n", "41                                            150-450  \n", "42                                  150,000 - 450,000  \n", "43                                          110 - 200  \n", "44                                            40 - 60  \n", "45                                               <150  \n", "46                                            10 - 40  \n", "47                                            0 - 4.1  \n", "48                                      0.00 - 100.00  >\n"]}], "source": ["print(df.head)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "heck_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 2}