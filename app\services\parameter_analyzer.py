"""
Parameter Analysis Service
Handles parameter analysis using standardized reference ranges dictionary
"""
import re
from typing import Dict, Any, Optional
from .reference_ranges import get_reference_range

class ParameterAnalyzer:
    """Analyzes medical parameters using only information from the reports"""

    def __init__(self):
        # No predefined categories or reference ranges
        # Everything will be based on report data
        #we can add a standard reference range dictionary here eventually
        pass
    
    def analyze_value_with_dictionary(self, param_name: str, value: str, unit: str = '', gender: str = None) -> Dict[str, Any]:
        """
        Analyze a parameter value using standardized reference ranges dictionary.

        COLOR CODING SYSTEM:
        🟢 GREEN (#28a745): Value is within the reference range (NORMAL)
        🔴 RED (#dc3545): Value is above the reference range (HIGH)
        🔵 BLUE (#007bff): Value is below the reference range (LOW)
        ⚫ GRAY (#6c757d): Non-numeric value or no reference range available
        """
        result = {
            'status': 'no-reference',
            'flag': '',
            'color': '#6c757d',  # ⚫ GRAY: Default for no reference
            'message': 'Reference range not specified',
            'numeric_value': None,
            'has_reference': False,
            'reference_source': 'none'
        }

        # Try to extract numeric value
        numeric_value = self._extract_numeric_value(value)
        if numeric_value is None:
            result['status'] = 'non-numeric'
            result['color'] = '#6c757d'  # ⚫ GRAY: Non-numeric
            result['message'] = 'Non-numeric value'
            return result

        result['numeric_value'] = numeric_value

        # Look up reference range in dictionary
        range_data = get_reference_range(param_name, gender=gender)
        if not range_data:
            result['message'] = 'Reference range not specified'
            return result

        result['has_reference'] = True
        result['reference_source'] = range_data.get('source', 'dictionary')

        # Handle multi-tier ranges
        if 'ranges' in range_data:
            for tier in range_data['ranges']:
                min_val = tier.get('min')
                max_val = tier.get('max')

                # Check if value falls in this tier
                in_range = True
                if min_val is not None and numeric_value < min_val:
                    in_range = False
                if max_val is not None and numeric_value > max_val:
                    in_range = False

                if in_range:
                    result['status'] = tier.get('status', 'normal')
                    result['color'] = self._get_color_code(tier.get('color', 'gray'))
                    result['message'] = f'{tier.get("status", "normal").replace("_", " ").title()}'

                    # Add directional flags for certain statuses
                    if 'low' in tier.get('status', ''):
                        result['flag'] = '↓'
                    elif 'high' in tier.get('status', ''):
                        result['flag'] = '↑'

                    break

        # Handle simple range
        elif 'range' in range_data:
            range_info = range_data['range']
            min_val = range_info.get('min')
            max_val = range_info.get('max')

            if min_val is not None and numeric_value < min_val:
                result['status'] = 'low'
                result['flag'] = '↓'
                result['color'] = '#007bff'  # 🔵 BLUE: Below range
                result['message'] = 'Below reference range'
            elif max_val is not None and numeric_value > max_val:
                result['status'] = 'high'
                result['flag'] = '↑'
                result['color'] = '#dc3545'  # 🔴 RED: Above range
                result['message'] = 'Above reference range'
            else:
                result['status'] = 'normal'
                result['color'] = '#28a745'  # 🟢 GREEN: Within range
                result['message'] = 'Within reference range'

        return result

    def _get_color_code(self, color_name: str) -> str:
        """Convert color name to hex code"""
        color_map = {
            'green': '#28a745',
            'red': '#dc3545',
            'blue': '#007bff',
            'yellow': '#ffc107',
            'orange': '#fd7e14',
            'gray': '#6c757d'
        }
        return color_map.get(color_name.lower(), '#6c757d')
    
    # Note: _parse_reference_range method removed - now using dictionary-based analysis
    
    def compare_values(self, value1: str, value2: str, param_name: str = '') -> Dict[str, Any]:
        """Compare two parameter values and return change information"""
        result = {
            'change': 0,
            'change_percent': 0,
            'trend': 'stable',
            'trend_icon': '→',
            'trend_color': '#6c757d',
            'significance': 'none'
        }
        
        num1 = self._extract_numeric_value(value1)
        num2 = self._extract_numeric_value(value2)
        
        if num1 is None or num2 is None:
            return result
        
        # Calculate change
        result['change'] = num2 - num1
        if num1 != 0:
            result['change_percent'] = (result['change'] / num1) * 100
        
        # Determine trend
        if abs(result['change_percent']) < 5:  # Less than 5% change
            result['trend'] = 'stable'
            result['trend_icon'] = '→'
            result['trend_color'] = '#6c757d'
        elif result['change'] > 0:
            result['trend'] = 'increasing'
            result['trend_icon'] = '↗'
            result['trend_color'] = '#dc3545' if result['change_percent'] > 20 else '#fd7e14'
        else:
            result['trend'] = 'decreasing'
            result['trend_icon'] = '↘'
            result['trend_color'] = '#007bff' if result['change_percent'] < -20 else '#20c997'
        
        # Determine significance
        if abs(result['change_percent']) > 50:
            result['significance'] = 'critical'
        elif abs(result['change_percent']) > 20:
            result['significance'] = 'significant'
        elif abs(result['change_percent']) > 10:
            result['significance'] = 'moderate'
        
        return result
    
    def _extract_numeric_value(self, value: str) -> Optional[float]:
        """Extract numeric value from a string"""
        if not value or value.strip() == '':
            return None
        
        # Remove common non-numeric parts
        cleaned = re.sub(r'[<>≤≥]', '', str(value).strip())
        
        # Try to find a number
        match = re.search(r'(\d+\.?\d*)', cleaned)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                pass
        
        return None
    
    #main method
    def analyze_value(self, param_name: str, value: str, unit: str = '', reference_range: str = '') -> Dict[str, Any]:
        """Analyze a parameter value using dictionary (reference_range ignored)"""
        return self.analyze_value_with_dictionary(param_name, value, unit)
