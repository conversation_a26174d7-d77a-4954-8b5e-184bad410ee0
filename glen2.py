import fitz
import json
import pandas as pd
from groq import Groq
import os
import re

client = Groq(api_key="********************************************************")
MODEL = "llama3-70b-8192"

def extract_text(pdf_path):
    return "\n".join([page.get_text() for page in fitz.open(pdf_path)])



def safe_json_extract(raw_response):
    try:
        # Try regular json parsing first
        return json.loads(raw_response)
    except json.JSONDecodeError:
        # Fallback: extract the first valid-looking JSON array
        match = re.search(r"\[\s*{.*?}\s*]", raw_response, re.DOTALL)
        if match:
            try:
                return json.loads(match.group())
            except:
                pass
        print("[!] LLM response could not be parsed. Raw output:")
        print(raw_response[:1000])
        return []


def compare_reports_with_llm(report_a_path, report_b_path):
    text_a = extract_text(report_a_path)
    text_b = extract_text(report_b_path)

    prompt = f"""
You are a medical report comparison expert.

Two lab reports are provided below. They may be from different labs and use slightly different formatting or terminology, but they describe the same patient and generally the same tests.

Your job is to:
1. Extract all medically important parameters from BOTH reports.
2. Align the parameters across reports (e.g., match 'HbA1c' with 'Glyco Hemoglobin').
3. Normalize units and use a single reference range for each parameter.
4. Return a JSON table comparing values from both reports.

Output format (as JSON list):
[
  {{
    "Parameter": "Hemoglobin",
    "Unit": "g/dL",
    "Reference Range": "13.0 - 17.0",
    "Report A": "15.4",
    "Report B": "14.6"
  }},
  ...
]

ONLY return this JSON. Do not include any explanation.

Report A:
\"\"\"
{text_a}
\"\"\"

Report B:
\"\"\"
{text_b}
\"\"\"
"""

    response = client.chat.completions.create(
        model=MODEL,
        messages=[{"role": "user", "content": prompt}],
        temperature=0
    )

    content = response.choices[0].message.content.strip()
    try:
        data = safe_json_extract(response.choices[0].message.content.strip())
        df = pd.DataFrame(data)
        df.to_csv("comparison_result_llm.csv", index=False)
        print("[✓] Comparison saved to comparison_result_llm.csv")
    except json.JSONDecodeError:
        print("[!] LLM did not return valid JSON. Output was:")
        print(content[:5000])

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 3:
        print("Usage: python compare_reports_llm.py report_a.pdf report_b.pdf")
    else:
        compare_reports_with_llm(sys.argv[1], sys.argv[2])
