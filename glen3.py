import fitz
import json
import os
import pandas as pd
import re
from groq import Groq

client = Groq(api_key="********************************************************")
MODEL = "llama3-70b-8192"

# --- Extract Text from PDF ---
def extract_text(path):
    return "\n".join([page.get_text() for page in fitz.open(path)])

# --- Clean + Reduce Report (optional) ---
def clean_text(text):
    lines = text.split("\n")
    filtered = [line for line in lines if re.search(r"\d", line)]
    return "\n".join(filtered[:400])  # reduce to ~400 lines max

# --- Call LLM to extract parameters from 1 report ---
def extract_parameters(report_text):
    prompt = f"""
You are a medical lab report interpreter.

Extract all medically important parameters from the following lab report. Return them as a JSON array with this format:
[
  {{
    "Parameter": "Hemoglobin",
    "Value": "15.4",
    "Unit": "g/dL",
    "Reference Range": "13.0 - 17.0"
  }},
  ...
]

Do not include any commentary or markdown. Only return valid JSON.

Report:
\"\"\"
{report_text}
\"\"\"
"""
    response = client.chat.completions.create(
        model=MODEL,
        messages=[{"role": "user", "content": prompt}],
        temperature=0
    )

    return safe_json_extract(response.choices[0].message.content.strip())

# --- Call LLM to align and compare the two parameter sets ---
def align_parameter_sets(params_a, params_b, label_a="Report A", label_b="Report B"):
    prompt = f"""
You are a medical report comparison expert.

Two sets of lab parameters are provided in JSON. Your job is to:
1. Align and match equivalent parameters (even if names differ slightly)
2. Normalize units and use a consistent reference range per parameter
3. Output a new JSON array with this structure:

[
  {{
    "Parameter": "Hemoglobin",
    "Unit": "g/dL",
    "Reference Range": "13.0 - 17.0",
    "{label_a}": "15.4",
    "{label_b}": "14.6"
  }},
  ...
]

Ensure all parameters from both inputs are included.
ONLY return valid JSON.

Report A JSON:
{json.dumps(params_a)}

Report B JSON:
{json.dumps(params_b)}
"""

    response = client.chat.completions.create(
        model=MODEL,
        messages=[{"role": "user", "content": prompt}],
        temperature=0
    )
    return safe_json_extract(response.choices[0].message.content.strip())

# --- Safe JSON parser with fallback ---
def safe_json_extract(text):
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        print("[!] Standard parse failed. Trying regex cleanup...")
        import re
        match = re.search(r'\[\s*{.*?}\s*]', text, re.DOTALL)
        if match:
            try:
                clean = re.sub(r',\s*]', ']', match.group())
                return json.loads(clean)
            except:
                pass
        print("[X] Could not parse JSON:\n", text[:1000])
        return []

# --- Main function to run full pipeline ---
def compare_reports(pdf_a_path, pdf_b_path):
    print("[*] Extracting and cleaning Report A")
    report_a = clean_text(extract_text(pdf_a_path))
    data_a = extract_parameters(report_a)

    print("[*] Extracting and cleaning Report B")
    report_b = clean_text(extract_text(pdf_b_path))
    data_b = extract_parameters(report_b)

    print("[*] Aligning both reports using LLM")
    comparison = align_parameter_sets(data_a, data_b,
                                      label_a=os.path.basename(pdf_a_path).replace(".pdf", ""),
                                      label_b=os.path.basename(pdf_b_path).replace(".pdf", ""))

    df = pd.DataFrame(comparison)
    df.to_csv("comparison_split_groq.csv", index=False)
    print("[✓] Comparison saved to comparison_split_groq.csv")

# --- CLI Entrypoint ---
if __name__ == "__main__":
    import sys
    if len(sys.argv) != 3:
        print("Usage: python glen3.py report1.pdf report2.pdf")
    else:
        compare_reports(sys.argv[1], sys.argv[2])
