import fitz
import re
import json
import os
import pandas as pd
from groq import Groq

client = Groq(api_key="********************************************************")
MODEL = "llama3-70b-8192"

# === Step 1: Better Text Extraction ===
def extract_text(pdf_path):
    print("\n[Step 1] Extracting raw text from PDF...\n")
    doc = fitz.open(pdf_path)
    all_text = []

    for i, page in enumerate(doc):
        blocks = page.get_text("blocks")
        blocks.sort(key=lambda b: (b[1], b[0]))  # Top-down, left-right
        page_text = "\n".join([b[4].strip() for b in blocks if b[4].strip()])
        print(f"\n--- Page {i+1} ---\n{page_text[:1000]}\n")
        all_text.append(page_text)

    return "\n\n".join(all_text)

# === Step 2: Clean and Filter ===
def clean_text(raw_text):
    print("\n[Step 2] Cleaning extracted text...\n")
    lines = raw_text.split("\n")
    cleaned = []
    seen = set()

    for line in lines:
        line = line.strip()
        if not line or line in seen:
            continue
        if re.search(r'(Printed on|Page \d+|Ref\. ID|Sample Type|Doctor Name|Report Date)', line, re.IGNORECASE):
            continue
        cleaned.append(line)
        seen.add(line)

    print("\n--- Cleaned Text Sample ---\n")
    for line in cleaned[:50]:
        print(line)
    return "\n".join(cleaned)

# === Step 3: Groq Extraction ===
def extract_parameters_with_llm(cleaned_text):
    print("\n[Step 3] Sending cleaned text to Groq...\n")
    prompt = f"""
You are a medical lab report parser.

Your task is to extract all medically important test parameters from the lab report below.
Return a JSON list where each item has the following fields:

- "Parameter": descriptive test name (e.g., "Hemoglobin")
- "Value": only the result value (e.g., "13.2" or "Positive")
- "Unit": unit of measurement (e.g., "g/dL", "mg/dL", "%")
- "Reference Range": standard range (e.g., "13.0 - 17.0" or "<5.7: Normal")

Important:
- DO NOT combine value and unit into one field
- Normalize units like "mg/dl" to "mg/dL"
- If unit or reference is missing, leave the field blank
- Include all tests found in the report

Return only valid JSON. No commentary.

Report:
\"\"\"
{cleaned_text}
\"\"\"
"""
    print("--- Prompt (first 1000 chars) ---")
    print(prompt[:1000])

    response = client.chat.completions.create(
        model=MODEL,
        messages=[{"role": "user", "content": prompt}],
        temperature=0
    )

    raw_output = response.choices[0].message.content.strip()
    print("\n--- Raw LLM Output (first 1000 chars) ---\n")
    print(raw_output[:1000])
    return safe_json_extract(raw_output)

# === Step 4: Safe JSON Parsing ===
def safe_json_extract(text):
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        print("[!] JSON decode failed. Trying regex cleanup...")
        match = re.search(r'\[\s*{.*?}\s*]', text, re.DOTALL)
        if match:
            try:
                clean = re.sub(r',\s*]', ']', match.group())
                return json.loads(clean)
            except:
                pass
        print("[!] Final raw output:\n", text[:1000])
        return []

# === Step 5: Run Whole Flow ===
def parse_pdf_report(pdf_path):
    print(f"\n=== Processing file: {pdf_path} ===\n")
    raw = extract_text(pdf_path)
    cleaned = clean_text(raw)

    with open("debug_cleaned_text.txt", "w") as f:
        f.write(cleaned)

    result = extract_parameters_with_llm(cleaned)
    print(f"\n[✓] Extracted {len(result)} parameters.\n")
    return result

# === CLI ===
if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print("Usage: python extract_lab_parameters_debug.py report.pdf")
        sys.exit(1)

    result = parse_pdf_report(sys.argv[1])
    df = pd.DataFrame(result)
    df.to_csv("debug_extracted_parameters.csv", index=False)
    print("\n[✓] Saved result to debug_extracted_parameters.csv")
