"""
Token Counter and Cost Estimator for GROQ API
Simple utility to estimate token usage and costs
"""

import re
from typing import Dict, Any, Optional


class TokenCounter:
    """Simple token counter and cost estimator for GROQ API"""
    
    # GROQ pricing (as of 2024 - update these based on current pricing)
    PRICING = {
        'llama3-70b-8192': {
            'input': 0.00059,   # per 1K tokens
            'output': 0.00079   # per 1K tokens
        },
        'llama3-8b-8192': {
            'input': 0.00005,   # per 1K tokens
            'output': 0.00008   # per 1K tokens
        },
        'mixtral-8x7b-32768': {
            'input': 0.00024,   # per 1K tokens
            'output': 0.00024   # per 1K tokens
        }
    }
    
    @staticmethod
    def estimate_tokens(text: str) -> int:
        """
        Estimate token count for text.
        This is a rough approximation: ~4 characters per token for English text.
        For more accuracy, you'd need the actual tokenizer, but this gives a good estimate.
        """
        if not text:
            return 0
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Rough estimation: 4 characters per token (conservative estimate)
        # This tends to overestimate slightly, which is good for cost planning
        estimated_tokens = len(text) / 4
        
        return int(estimated_tokens)
    
    @staticmethod
    def estimate_cost(prompt_tokens: int, completion_tokens: int, model: str) -> float:
        """Calculate estimated cost based on token usage"""
        if model not in TokenCounter.PRICING:
            # Default to most expensive model for safety
            model = 'llama3-70b-8192'
        
        pricing = TokenCounter.PRICING[model]
        input_cost = (prompt_tokens / 1000) * pricing['input']
        output_cost = (completion_tokens / 1000) * pricing['output']
        
        return round(input_cost + output_cost, 6)
    
    @staticmethod
    def get_usage_info(prompt: str, response: str, model: str) -> Dict[str, Any]:
        """Get comprehensive usage information for an API call"""
        prompt_tokens = TokenCounter.estimate_tokens(prompt)
        completion_tokens = TokenCounter.estimate_tokens(response)
        total_tokens = prompt_tokens + completion_tokens
        estimated_cost = TokenCounter.estimate_cost(prompt_tokens, completion_tokens, model)
        
        return {
            'prompt_tokens': prompt_tokens,
            'completion_tokens': completion_tokens,
            'total_tokens': total_tokens,
            'estimated_cost': estimated_cost,
            'model': model,
            'prompt_length': len(prompt),
            'response_length': len(response)
        }
    
    @staticmethod
    def format_cost_summary(usage_info: Dict[str, Any]) -> str:
        """Format a human-readable cost summary"""
        return f"""
Token Usage Summary:
- Model: {usage_info['model']}
- Prompt tokens: {usage_info['prompt_tokens']:,}
- Completion tokens: {usage_info['completion_tokens']:,}
- Total tokens: {usage_info['total_tokens']:,}
- Estimated cost: ${usage_info['estimated_cost']:.6f}
- Prompt length: {usage_info['prompt_length']:,} characters
- Response length: {usage_info['response_length']:,} characters
"""


class APICallLogger:
    """Simple logger for API calls and their costs"""
    
    def __init__(self):
        self.calls = []
        self.total_cost = 0.0
        self.total_tokens = 0
    
    def log_call(self, usage_info: Dict[str, Any], operation_type: str = "unknown"):
        """Log an API call"""
        call_record = {
            **usage_info,
            'operation_type': operation_type,
            'timestamp': self._get_timestamp()
        }
        
        self.calls.append(call_record)
        self.total_cost += usage_info['estimated_cost']
        self.total_tokens += usage_info['total_tokens']
        
        return call_record
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of all logged calls"""
        if not self.calls:
            return {
                'total_calls': 0,
                'total_cost': 0.0,
                'total_tokens': 0,
                'average_cost_per_call': 0.0,
                'calls': []
            }
        
        return {
            'total_calls': len(self.calls),
            'total_cost': round(self.total_cost, 6),
            'total_tokens': self.total_tokens,
            'average_cost_per_call': round(self.total_cost / len(self.calls), 6),
            'calls': self.calls
        }
    
    def print_summary(self):
        """Print a formatted summary"""
        summary = self.get_summary()
        
        print("\n" + "="*60)
        print("GROQ API USAGE SUMMARY")
        print("="*60)
        print(f"Total API calls: {summary['total_calls']}")
        print(f"Total tokens used: {summary['total_tokens']:,}")
        print(f"Total estimated cost: ${summary['total_cost']:.6f}")
        print(f"Average cost per call: ${summary['average_cost_per_call']:.6f}")
        
        if summary['calls']:
            print("\nRecent calls:")
            for i, call in enumerate(summary['calls'][-5:], 1):  # Show last 5 calls
                print(f"  {i}. {call['operation_type']} - {call['total_tokens']:,} tokens - ${call['estimated_cost']:.6f}")
        
        print("="*60)
    
    def _get_timestamp(self):
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')


# Global logger instance for easy access
api_logger = APICallLogger()


def track_api_call(prompt: str, response: str, model: str, operation_type: str = "unknown") -> Dict[str, Any]:
    """
    Convenience function to track an API call and return usage info
    """
    usage_info = TokenCounter.get_usage_info(prompt, response, model)
    call_record = api_logger.log_call(usage_info, operation_type)
    
    # Print immediate feedback
    print(f"\n💰 API Call Tracked:")
    print(f"   Operation: {operation_type}")
    print(f"   Tokens: {usage_info['total_tokens']:,} ({usage_info['prompt_tokens']:,} + {usage_info['completion_tokens']:,})")
    print(f"   Cost: ${usage_info['estimated_cost']:.6f}")
    print(f"   Running total: ${api_logger.total_cost:.6f}")
    
    return call_record


def get_cost_projections(daily_calls: int, tokens_per_call: int, model: str = 'llama3-70b-8192') -> Dict[str, float]:
    """
    Calculate cost projections based on usage patterns
    """
    # Assume 70% prompt tokens, 30% completion tokens (typical for extraction tasks)
    prompt_tokens = int(tokens_per_call * 0.7)
    completion_tokens = int(tokens_per_call * 0.3)
    
    cost_per_call = TokenCounter.estimate_cost(prompt_tokens, completion_tokens, model)
    
    return {
        'cost_per_call': cost_per_call,
        'daily_cost': cost_per_call * daily_calls,
        'weekly_cost': cost_per_call * daily_calls * 7,
        'monthly_cost': cost_per_call * daily_calls * 30,
        'yearly_cost': cost_per_call * daily_calls * 365
    }


if __name__ == "__main__":
    # Example usage
    sample_prompt = "Extract medical parameters from this lab report: " + "Sample lab report text here..." * 100
    sample_response = '{"parameters": [{"name": "Hemoglobin", "value": "13.2", "unit": "g/dL"}]}'
    
    # Track a sample call
    track_api_call(sample_prompt, sample_response, 'llama3-70b-8192', 'extract_parameters')
    
    # Print summary
    api_logger.print_summary()
    
    # Show cost projections
    projections = get_cost_projections(daily_calls=10, tokens_per_call=2000)
    print(f"\nCost projections for 10 calls/day with 2000 tokens/call:")
    for period, cost in projections.items():
        print(f"  {period}: ${cost:.2f}")
