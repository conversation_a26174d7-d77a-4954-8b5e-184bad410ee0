{% extends "base.html" %}

{% block title %}Token Usage & Cost Estimation - Medical Report Tracker{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-coins"></i> Token Usage & Cost Estimation</h2>
            <div>
                <button onclick="location.reload()" class="btn btn-outline-primary">
                    <i class="fas fa-sync"></i> Refresh
                </button>
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-home"></i> Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Current Session Usage -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Current Session Usage</h5>
            </div>
            <div class="card-body">
                {% if session_usage.total_calls > 0 %}
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3>{{ session_usage.total_calls }}</h3>
                                <p class="mb-0">API Calls</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3>{{ "{:,}".format(session_usage.total_tokens) }}</h3>
                                <p class="mb-0">Total Tokens</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3>${{ "%.6f"|format(session_usage.total_cost) }}</h3>
                                <p class="mb-0">Total Cost</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3>${{ "%.6f"|format(session_usage.average_cost_per_call) }}</h3>
                                <p class="mb-0">Avg Cost/Call</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h6>Recent API Calls:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Operation</th>
                                    <th>Model</th>
                                    <th>Tokens</th>
                                    <th>Cost</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for call in session_usage.calls[-10:] %}
                                <tr>
                                    <td>{{ call.timestamp }}</td>
                                    <td>
                                        <span class="badge badge-secondary">{{ call.operation_type }}</span>
                                    </td>
                                    <td>{{ call.model }}</td>
                                    <td>{{ "{:,}".format(call.total_tokens) }}</td>
                                    <td>${{ "%.6f"|format(call.estimated_cost) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No API calls made yet</h5>
                    <p class="text-muted">Upload and process some reports to see token usage statistics.</p>
                    <a href="/upload" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Report
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Cost Projections -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calculator"></i> Cost Projections</h5>
                <small class="text-muted">Based on {{ "{:,}".format(avg_tokens_per_call) }} tokens per call average</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>llama3-70b-8192 (Most Capable)</h6>
                        {% set model_projections = projections %}
                        <ul class="list-unstyled">
                            <li><strong>Per call:</strong> ${{ "%.6f"|format(model_projections.cost_per_call) }}</li>
                            <li><strong>Daily (10 calls):</strong> ${{ "%.4f"|format(model_projections.daily_cost) }}</li>
                            <li><strong>Weekly:</strong> ${{ "%.2f"|format(model_projections.weekly_cost) }}</li>
                            <li><strong>Monthly:</strong> ${{ "%.2f"|format(model_projections.monthly_cost) }}</li>
                            <li><strong>Yearly:</strong> ${{ "%.2f"|format(model_projections.yearly_cost) }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Model Comparison (per call)</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Model</th>
                                        <th>Cost/Call</th>
                                        <th>Monthly (10/day)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>llama3-8b-8192</td>
                                        <td>$0.000040</td>
                                        <td>$0.12</td>
                                    </tr>
                                    <tr>
                                        <td>mixtral-8x7b-32768</td>
                                        <td>$0.000192</td>
                                        <td>$0.58</td>
                                    </tr>
                                    <tr class="table-warning">
                                        <td>llama3-70b-8192</td>
                                        <td>${{ "%.6f"|format(model_projections.cost_per_call) }}</td>
                                        <td>${{ "%.2f"|format(model_projections.monthly_cost) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Usage Scenarios -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Usage Scenarios</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">Light Usage</h6>
                                <small>5 reports/day</small>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><strong>Daily:</strong> ${{ "%.4f"|format(model_projections.daily_cost / 2) }}</li>
                                    <li><strong>Monthly:</strong> ${{ "%.2f"|format(model_projections.monthly_cost / 2) }}</li>
                                    <li><strong>Yearly:</strong> ${{ "%.2f"|format(model_projections.yearly_cost / 2) }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-white">
                                <h6 class="mb-0">Medium Usage</h6>
                                <small>10 reports/day</small>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><strong>Daily:</strong> ${{ "%.4f"|format(model_projections.daily_cost) }}</li>
                                    <li><strong>Monthly:</strong> ${{ "%.2f"|format(model_projections.monthly_cost) }}</li>
                                    <li><strong>Yearly:</strong> ${{ "%.2f"|format(model_projections.yearly_cost) }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">Heavy Usage</h6>
                                <small>50 reports/day</small>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><strong>Daily:</strong> ${{ "%.4f"|format(model_projections.daily_cost * 5) }}</li>
                                    <li><strong>Monthly:</strong> ${{ "%.2f"|format(model_projections.monthly_cost * 5) }}</li>
                                    <li><strong>Yearly:</strong> ${{ "%.2f"|format(model_projections.yearly_cost * 5) }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tips for Cost Optimization -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Cost Optimization Tips</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Model Selection:</h6>
                        <ul>
                            <li><strong>llama3-8b-8192:</strong> Best for simple parameter extraction (cheapest)</li>
                            <li><strong>mixtral-8x7b-32768:</strong> Good balance of capability and cost</li>
                            <li><strong>llama3-70b-8192:</strong> Most capable but most expensive</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Optimization Strategies:</h6>
                        <ul>
                            <li>Keep prompts concise but clear</li>
                            <li>Use smaller models for routine extraction</li>
                            <li>Batch process multiple reports when possible</li>
                            <li>Monitor token usage regularly</li>
                            <li>Set up cost alerts and budgets</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh every 30 seconds if there are active calls
{% if session_usage.total_calls > 0 %}
setTimeout(function() {
    location.reload();
}, 30000);
{% endif %}
</script>
{% endblock %}
